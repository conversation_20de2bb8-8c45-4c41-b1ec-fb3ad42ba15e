// Discord Webhook Integration Module
import { DISCORD_CONFIG } from '../../config.js';

/**
 * Send chat session to Discord via webhook
 * @param {Object} chatData - The chat session data to send
 * @param {string} webhookUrl - Discord webhook URL
 * @returns {Promise<Object>} - Result of the send operation
 */
export async function sendChatToDiscord(chatData, webhookUrl) {
    try {
        if (!webhookUrl) {
            throw new Error('Webhook URL is required');
        }

        console.log('Discord: Formatting chat data:', chatData);
        const embeds = formatChatAsDiscordEmbed(chatData);
        console.log('Discord: Formatted chat embeds:', embeds);
        console.log('Discord: Embeds type check:', Array.isArray(embeds), typeof embeds);

        // Validate embeds before sending
        if (!Array.isArray(embeds) || embeds.length === 0) {
            throw new Error('Invalid embeds format');
        }

        // Validate each embed
        for (let i = 0; i < embeds.length; i++) {
            const embed = embeds[i];
            if (!embed || typeof embed !== 'object') {
                throw new Error(`Invalid embed at index ${i}: ${typeof embed}`);
            }
        }

        const result = await sendDiscordWebhook(webhookUrl, embeds);

        return {
            success: true,
            messageId: result.messageId,
            result: result
        };

    } catch (error) {
        console.error('Error sending chat to Discord:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send analysis results to Discord via webhook
 * @param {Object} analysisData - The analysis data to send
 * @param {string} webhookUrl - Discord webhook URL
 * @returns {Promise<Object>} - Result of the send operation
 */
export async function sendAnalysisToDiscord(analysisData, webhookUrl) {
    try {
        if (!webhookUrl) {
            throw new Error('Webhook URL is required');
        }

        console.log('Discord: Formatting analysis data:', analysisData);
        const embeds = formatAnalysisAsDiscordEmbed(analysisData);
        console.log('Discord: Formatted embeds:', embeds);
        console.log('Discord: Embeds type check:', Array.isArray(embeds), typeof embeds);

        // Validate embeds before sending
        if (!Array.isArray(embeds) || embeds.length === 0) {
            throw new Error('Invalid embeds format');
        }

        // Validate each embed
        for (let i = 0; i < embeds.length; i++) {
            const embed = embeds[i];
            if (!embed || typeof embed !== 'object') {
                throw new Error(`Invalid embed at index ${i}: ${typeof embed}`);
            }
        }

        const result = await sendDiscordWebhook(webhookUrl, embeds);

        return {
            success: true,
            messageId: result.messageId,
            result: result
        };

    } catch (error) {
        console.error('Error sending to Discord:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send a message to Discord using webhook
 * @param {string} webhookUrl - Webhook URL
 * @param {Object} embed - Discord embed object
 * @returns {Promise<Object>} - API response
 */
async function sendDiscordWebhook(webhookUrl, embeds) {
    try {
        console.log('Discord: Received embeds for sending:', embeds);

        // Validate and use the provided embeds
        let validEmbeds = embeds;

        // If embeds are invalid or empty, create a fallback
        if (!Array.isArray(embeds) || embeds.length === 0) {
            console.warn('Discord: No valid embeds provided, using fallback');
            validEmbeds = [{
                title: "Agent Hustle Analysis",
                description: "Analysis completed successfully",
                color: 0x5BA9F9,
                timestamp: new Date().toISOString()
            }];
        }

        // Ensure embeds don't exceed Discord limits (max 10 embeds)
        if (validEmbeds.length > 10) {
            console.warn('Discord: Too many embeds, truncating to 10');
            validEmbeds = validEmbeds.slice(0, 10);
        }

        const payload = {
            embeds: validEmbeds,
            username: 'Agent Hustle Pro'
        };

        console.log('Discord: Sending payload with', validEmbeds.length, 'embeds');

        let lastError;
        
        for (let attempt = 1; attempt <= DISCORD_CONFIG.RETRY_ATTEMPTS; attempt++) {
            try {
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload),
                    signal: AbortSignal.timeout(DISCORD_CONFIG.TIMEOUT)
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
                
                return {
                    success: true,
                    messageId: 'sent',
                    attempt: attempt
                };
                
            } catch (error) {
                lastError = error;
                console.warn(`Discord send attempt ${attempt} failed:`, error.message);
                
                if (attempt < DISCORD_CONFIG.RETRY_ATTEMPTS) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                }
            }
        }
        
        throw lastError;
    } catch (error) {
        console.error('Discord webhook error:', error);
        throw error;
    }
}

/**
 * Format analysis data as Discord embed
 * @param {Object} analysisData - Analysis data object
 * @returns {Array} - Array of Discord embed objects
 */
export function formatAnalysisAsDiscordEmbed(analysisData) {
    try {
        console.log('Discord: Formatting analysis data:', analysisData);

        // Validate input
        if (!analysisData || typeof analysisData !== 'object') {
            console.error('Discord: Invalid analysisData:', analysisData);
            throw new Error('Invalid analysis data');
        }

        const { analysisType, result, date } = analysisData;

        // Create professional header embed using Telegram formatting principles
        const headerEmbed = {
            title: '🤖 Agent Hustle Analysis Report',
            color: 0x5BA9F9,
            timestamp: new Date(date || Date.now()).toISOString(),
            footer: {
                text: '🚀 Powered by Agent Hustle Pro • Professional AI Analysis at Your Fingertips'
            },
            fields: []
        };

        // Add analysis type with emoji
        if (analysisType) {
            headerEmbed.fields.push({
                name: '📊 Analysis Type',
                value: String(analysisType).substring(0, 1024),
                inline: true
            });
        }

        // Add formatted date with emoji
        const formattedDate = new Date(date || Date.now()).toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            timeZoneName: 'short'
        });

        headerEmbed.fields.push({
            name: '📅 Generated',
            value: formattedDate,
            inline: true
        });

        // Process content using Telegram formatting principles
        let content = '';
        let summary = '';
        let keyPoints = [];

        if (typeof result === 'string') {
            content = result;
        } else if (result && typeof result === 'object') {
            content = result.content || result.analysis || JSON.stringify(result);
            summary = result.summary || '';
            keyPoints = result.keyPoints || [];
        }

        // Clean content using Telegram-inspired approach
        const cleanContent = cleanAnalysisContent(content);
        const cleanSummary = cleanAnalysisContent(summary);

        console.log('Discord: Content length:', cleanContent.length);
        console.log('Discord: Has summary:', !!cleanSummary);
        console.log('Discord: Key points:', keyPoints.length);

        // Add executive summary if available
        if (cleanSummary && cleanSummary.trim()) {
            headerEmbed.fields.push({
                name: '📋 Executive Summary',
                value: cleanSummary.substring(0, 1024),
                inline: false
            });
        }

        // Handle content based on length
        if (cleanContent) {
            // If content is short enough, add to header embed
            if (cleanContent.length <= 1024) {
                headerEmbed.fields.push({
                    name: '📝 Detailed Analysis',
                    value: cleanContent || 'No content available',
                    inline: false
                });

                // Add key points if available and space permits
                if (keyPoints.length > 0 && headerEmbed.fields.length < 25) {
                    const keyPointsText = formatKeyPoints(keyPoints);
                    if (keyPointsText.length <= 1024) {
                        headerEmbed.fields.push({
                            name: '🔑 Key Insights',
                            value: keyPointsText,
                            inline: false
                        });
                    }
                }

                console.log('Discord: Added content to header embed');
                return [headerEmbed];
            } else {
                // Split long content into multiple embeds
                const embeds = [headerEmbed];
                const maxFieldLength = 1024;
                let remainingContent = cleanContent;
                let partNumber = 1;

                while (remainingContent.length > 0) {
                    let chunk = remainingContent.substring(0, maxFieldLength);
                    let finalChunk = chunk;
                    let nextStartIndex = maxFieldLength;

                    // Try to break at natural boundaries if there's more content
                    if (remainingContent.length > maxFieldLength) {
                        const lastParagraph = chunk.lastIndexOf('\n\n');
                        const lastSentence = chunk.lastIndexOf('. ');
                        const lastQuestion = chunk.lastIndexOf('? ');
                        const lastExclamation = chunk.lastIndexOf('! ');
                        const lastNewline = chunk.lastIndexOf('\n');
                        const lastSpace = chunk.lastIndexOf(' ');

                        // Find the best break point (prefer complete sentences)
                        const sentenceBreaks = [lastParagraph, lastSentence, lastQuestion, lastExclamation];
                        const bestSentenceBreak = Math.max(...sentenceBreaks);

                        let breakPoint = -1;

                        // Prefer paragraph breaks first
                        if (lastParagraph > maxFieldLength * 0.6) {
                            breakPoint = lastParagraph + 2; // Include the \n\n
                        }
                        // Then complete sentences
                        else if (bestSentenceBreak > maxFieldLength * 0.6) {
                            breakPoint = bestSentenceBreak + 2; // Include the punctuation and space
                        }
                        // Then line breaks
                        else if (lastNewline > maxFieldLength * 0.7) {
                            breakPoint = lastNewline + 1; // Include the \n
                        }
                        // Finally word boundaries
                        else if (lastSpace > maxFieldLength * 0.8) {
                            breakPoint = lastSpace + 1; // Include the space
                        }

                        if (breakPoint > 0) {
                            finalChunk = remainingContent.substring(0, breakPoint).trim();
                            nextStartIndex = breakPoint;
                        }
                    }

                    // Update remaining content
                    remainingContent = remainingContent.substring(nextStartIndex).trim();

                    // Ensure finalChunk is not empty and doesn't start with orphaned text
                    if (finalChunk.length === 0) {
                        break; // Prevent infinite loop
                    }

                    // Clean up any orphaned characters at the start
                    finalChunk = finalChunk.replace(/^[^\w\s]*\s*/, '').trim();

                    // Skip if chunk is too short to be meaningful
                    if (finalChunk.length < 10) {
                        continue;
                    }

                    // Create professional content embed
                    const contentEmbed = {
                        color: 0x5BA9F9,
                        fields: [{
                            name: partNumber === 1 ? '📝 Detailed Analysis' : `📝 Detailed Analysis (Part ${partNumber})`,
                            value: finalChunk,
                            inline: false
                        }]
                    };

                    embeds.push(contentEmbed);
                    partNumber++;

                    // Discord limit: max 10 embeds per message
                    if (embeds.length >= 10) {
                        console.log('Discord: Reached embed limit, truncating remaining content');
                        break;
                    }
                }

                // Add key points to the last embed if available
                if (keyPoints.length > 0 && embeds.length < 10) {
                    const keyPointsText = formatKeyPoints(keyPoints);
                    if (keyPointsText.length <= 1024) {
                        const lastEmbed = embeds[embeds.length - 1];
                        if (lastEmbed.fields.length < 25) {
                            lastEmbed.fields.push({
                                name: '🔑 Key Insights',
                                value: keyPointsText,
                                inline: false
                            });
                        } else {
                            // Create separate embed for key points
                            embeds.push({
                                color: 0x5BA9F9,
                                fields: [{
                                    name: '🔑 Key Insights',
                                    value: keyPointsText,
                                    inline: false
                                }]
                            });
                        }
                    }
                }

                console.log(`Discord: Split content into ${embeds.length} embeds`);
                return embeds;
            }
        } else {
            embed.fields.push({
                name: 'Analysis Results',
                value: 'No content available',
                inline: false
            });
        }

        console.log('Discord: Created simple embed:', embed);
        return [embed];

    } catch (error) {
        console.error('Error formatting Discord embed:', error);
        console.error('Error details:', error.stack);
        console.log('Fallback: Using simple embed for analysisData:', analysisData);

        // Extract content safely for fallback
        let fallbackContent = 'Analysis completed successfully';
        let fallbackType = 'General Analysis';

        try {
            if (analysisData) {
                fallbackType = String(analysisData.analysisType || 'General Analysis');

                if (analysisData.result) {
                    if (typeof analysisData.result === 'string') {
                        fallbackContent = analysisData.result.substring(0, 1000);
                    } else if (typeof analysisData.result === 'object' && analysisData.result.content) {
                        fallbackContent = String(analysisData.result.content).substring(0, 1000);
                    } else {
                        fallbackContent = JSON.stringify(analysisData.result).substring(0, 1000);
                    }
                }
            }
        } catch (fallbackError) {
            console.error('Error in fallback processing:', fallbackError);
        }

        // Return a simple fallback embed with actual content
        return [{
            title: '🤖 Agent Hustle Analysis Report',
            description: fallbackContent,
            color: 0x5BA9F9,
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Powered by Agent Hustle Pro'
            },
            fields: [
                {
                    name: '� Analysis Type',
                    value: fallbackType.substring(0, 1024),
                    inline: true
                },
                {
                    name: '� Generated',
                    value: new Date().toLocaleString(),
                    inline: true
                }
            ]
        }];
    }
}

/**
 * Format chat session data as Discord embed
 * @param {Object} chatData - Chat session data object
 * @returns {Array} - Array of Discord embed objects
 */
export function formatChatAsDiscordEmbed(chatData) {
    try {
        console.log('Discord: Formatting chat data:', chatData);

        // Validate input
        if (!chatData || typeof chatData !== 'object') {
            console.error('Discord: Invalid chatData:', chatData);
            throw new Error('Invalid chat data');
        }

        const { title, messages, createdAt, updatedAt, messageCount } = chatData;
        const embeds = [];

        // Create professional header embed
        const headerEmbed = {
            title: '💬 Agent Hustle Chat Session',
            color: 0x5BA9F9,
            timestamp: new Date(updatedAt || createdAt || Date.now()).toISOString(),
            footer: {
                text: '🚀 Powered by Agent Hustle Pro • Professional AI Chat at Your Fingertips'
            },
            fields: [
                {
                    name: '📝 Title',
                    value: title || 'Untitled Chat',
                    inline: true
                },
                {
                    name: '📊 Messages',
                    value: String(messageCount || (messages ? messages.length : 0)),
                    inline: true
                },
                {
                    name: '📅 Created',
                    value: formatDiscordDate(createdAt || new Date()),
                    inline: true
                }
            ]
        };

        if (updatedAt && updatedAt !== createdAt) {
            headerEmbed.fields.push({
                name: '🔄 Last Updated',
                value: formatDiscordDate(updatedAt),
                inline: true
            });
        }

        embeds.push(headerEmbed);

        // Add conversation messages
        if (messages && Array.isArray(messages) && messages.length > 0) {
            let conversationText = '';

            messages.forEach((msg, index) => {
                const role = msg.role === 'user' ? '👤 **You**' : '🤖 **Assistant**';
                const content = cleanChatMessageContent(msg.content);

                const messageText = `${role}:\n${content}\n\n`;

                // Check if adding this message would exceed Discord's field limit
                if (conversationText.length + messageText.length > 1000) {
                    // Create an embed for the current conversation text
                    if (conversationText.trim()) {
                        embeds.push({
                            color: 0x5BA9F9,
                            fields: [{
                                name: '💭 Conversation',
                                value: conversationText.trim(),
                                inline: false
                            }]
                        });
                    }
                    conversationText = messageText;
                } else {
                    conversationText += messageText;

                    // Add separator between messages (except for the last one)
                    if (index < messages.length - 1) {
                        conversationText += '─────────────────────────────────────\n\n';
                    }
                }
            });

            // Add any remaining conversation text
            if (conversationText.trim()) {
                embeds.push({
                    color: 0x5BA9F9,
                    fields: [{
                        name: '💭 Conversation',
                        value: conversationText.trim(),
                        inline: false
                    }]
                });
            }
        } else {
            embeds.push({
                color: 0x5BA9F9,
                fields: [{
                    name: '💭 Conversation',
                    value: '📭 *No messages in this chat session*',
                    inline: false
                }]
            });
        }

        console.log(`Discord: Created ${embeds.length} embeds for chat`);
        return embeds;

    } catch (error) {
        console.error('Error formatting Discord chat embed:', error);
        console.error('Error details:', error.stack);
        console.log('Fallback: Using simple embed for chatData:', chatData);

        // Fallback embed
        return [{
            title: '💬 Agent Hustle Chat Session',
            color: 0x5BA9F9,
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Powered by Agent Hustle Pro'
            },
            fields: [
                {
                    name: '📝 Title',
                    value: (chatData?.title || 'Untitled Chat').substring(0, 1024),
                    inline: true
                },
                {
                    name: '📊 Messages',
                    value: String(chatData?.messageCount || 0),
                    inline: true
                },
                {
                    name: '📅 Created',
                    value: new Date().toLocaleString(),
                    inline: true
                }
            ]
        }];
    }
}

/**
 * Clean and format chat message content for Discord
 * @param {string} content - Raw message content
 * @returns {string} - Cleaned content
 */
function cleanChatMessageContent(content) {
    if (!content || typeof content !== 'string') {
        return '';
    }

    let cleaned = String(content).trim();

    // Remove HTML tags
    cleaned = cleaned.replace(/<[^>]*>/g, '');

    // Normalize whitespace
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
    cleaned = cleaned.replace(/[ \t]+/g, ' ');

    // Escape Discord markdown characters
    cleaned = cleaned.replace(/([*_`~|\\])/g, '\\$1');

    // Truncate if too long for Discord field
    if (cleaned.length > 800) {
        cleaned = cleaned.substring(0, 800) + '...\n\n*[Message truncated]*';
    }

    return cleaned.trim();
}

/**
 * Format date for Discord display
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date string
 */
function formatDiscordDate(date) {
    try {
        const d = new Date(date);
        return d.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return new Date().toLocaleString();
    }
}

// Helper functions inspired by Telegram formatting guide

/**
 * Clean analysis content using Telegram formatting principles
 * Minimal escaping, preserve readability
 */
function cleanAnalysisContent(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    return String(text)
        .trim()
        // Remove HTML tags
        .replace(/<[^>]*>/g, '')
        // Normalize whitespace (max 2 consecutive line breaks)
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        // Normalize spaces and tabs
        .replace(/[ \t]+/g, ' ')
        // Add line breaks before numbered lists
        .replace(/^(\d+\.\s)/gm, '\n$1')
        // Add line breaks before bullet points
        .replace(/^(-\s|\*\s|•\s)/gm, '\n$1')
        // Only escape characters that break Discord formatting
        .replace(/`/g, '\\`')  // Escape backticks
        .replace(/\*/g, '\\*') // Escape asterisks
        .replace(/_/g, '\\_')  // Escape underscores
        .trim();
}

/**
 * Format key points as numbered list
 */
function formatKeyPoints(keyPoints) {
    if (!Array.isArray(keyPoints) || keyPoints.length === 0) {
        return '';
    }

    return keyPoints
        .slice(0, 10) // Limit to 10 points
        .map((point, index) => `${index + 1}. ${cleanAnalysisContent(point)}`)
        .join('\n');
}

export async function testDiscordWebhook(webhookUrl) {
    try {
        if (!webhookUrl) {
            throw new Error('Webhook URL is required');
        }
        
        if (!validateWebhookUrl(webhookUrl)) {
            throw new Error('Invalid webhook URL format');
        }
        
        const testEmbed = {
            title: '🔧 Test Message',
            description: 'Discord webhook integration is working correctly!',
            color: 0x00FF00,
            timestamp: new Date().toISOString(),
            footer: {
                text: 'Agent Hustle Pro Analyzer'
            },
            fields: [
                {
                    name: '⏰ Time',
                    value: formatDate(new Date()),
                    inline: true
                },
                {
                    name: '✅ Status',
                    value: 'Connection Successful',
                    inline: true
                }
            ]
        };
        
        const payload = {
            embeds: [testEmbed],
            username: 'Agent Hustle Pro',
            content: '🚀 **Discord Integration Test**'
        };
        
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload),
            signal: AbortSignal.timeout(DISCORD_CONFIG.TIMEOUT)
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        return {
            success: true,
            message: 'Discord webhook test successful!'
        };
        
    } catch (error) {
        console.error('Discord webhook test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

export function validateWebhookUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    
    const webhookRegex = /^https:\/\/discord\.com\/api\/webhooks\/\d+\/[\w-]+$/;
    return webhookRegex.test(url.trim());
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        date = new Date();
    }
    
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
    });
} 
