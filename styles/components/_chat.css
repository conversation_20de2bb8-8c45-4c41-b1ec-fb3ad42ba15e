/* Chat Container Styles */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 480px; /* Fixed height optimized for popup extension */
    background: #1D1A2A;
    border-radius: 12px;
    border: 2px solid #2C2738;
    overflow: hidden;
    margin-bottom: 16px;
    transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Full-screen chat container */
.chat-container.full-screen {
    height: 520px; /* Maximize available space in popup */
    /* Max height restriction removed for better space utilization */
    border-radius: 16px;
    border-color: #5BA9F9;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Chat container with dock */
.chat-container.with-dock {
    height: 400px; /* Account for dock space while maximizing chat area */
    margin-bottom: 80px; /* Space for fixed dock */
}

/* Chat Messages Area */
.chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    scroll-behavior: smooth;
    background: #1D1A2A;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #4A98E8;
}

/* Message Styles */
.message {
    display: flex;
    flex-direction: column;
    max-width: 85%;
    word-wrap: break-word;
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    align-self: flex-end;
    background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
    color: #F9F9F9;
    border: 1px solid rgba(91, 169, 249, 0.3);
}

.message.assistant {
    align-self: flex-start;
    background: #2C2738;
    border-radius: 18px 18px 18px 4px;
    padding: 12px 16px;
    color: #F9F9F9;
    border: 1px solid #3D3548;
}

.message.streaming {
    position: relative;
}

.message.streaming::after {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: pulse 1.5s infinite;
    margin-left: 8px;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

/* Message Header */
.message-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 12px;
    opacity: 0.8;
}

.message-role {
    font-weight: 600;
    color: #5BA9F9;
}

.message.user .message-role {
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 11px;
    color: #B2AFC5;
}

.streaming-indicator {
    color: #5BA9F9;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Message Content */
.message-content {
    line-height: 1.5;
    font-size: 14px;
}

.message-content strong {
    font-weight: 600;
    color: #F9F9F9;
}

.message-content em {
    font-style: italic;
    color: #E0DDE8;
}

.message-content .inline-code {
    background: rgba(91, 169, 249, 0.15);
    color: #5BA9F9;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 13px;
}

.message-content .code-block {
    background: #1A1A2E;
    color: #F9F9F9;
    padding: 12px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 13px;
    overflow-x: auto;
    border: 1px solid #2C2738;
    margin: 8px 0;
}

/* Tool Executions */
.tool-executions {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.tool-execution {
    background: rgba(91, 169, 249, 0.1);
    border: 1px solid rgba(91, 169, 249, 0.2);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
}

.tool-execution.pending {
    border-color: rgba(255, 193, 7, 0.3);
    background: rgba(255, 193, 7, 0.1);
}

.tool-execution.success {
    border-color: rgba(40, 167, 69, 0.3);
    background: rgba(40, 167, 69, 0.1);
}

.tool-execution.error {
    border-color: rgba(220, 53, 69, 0.3);
    background: rgba(220, 53, 69, 0.1);
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
}

.tool-name {
    color: #5BA9F9;
}

.tool-status {
    font-size: 11px;
    color: #B2AFC5;
}

.tool-details {
    margin-top: 6px;
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    font-size: 11px;
    color: #B2AFC5;
    background: rgba(0, 0, 0, 0.2);
    padding: 6px;
    border-radius: 4px;
}

/* Chat Input Area */
.chat-input-area {
    border-top: 1px solid #2C2738;
    background: #1A1A2E;
    padding: 16px;
}

.chat-input-container {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#chatInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #2C2738;
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #1D1A2A;
    color: #F9F9F9;
    font-family: inherit;
    resize: none;
    min-height: 40px;
    max-height: 120px;
    overflow-y: auto;
}

#chatInput:focus {
    outline: none;
    border-color: #5BA9F9;
    box-shadow: 0 0 0 3px rgba(91, 169, 249, 0.15);
}

#chatInput::placeholder {
    color: #B2AFC5;
}

.chat-send-btn {
    padding: 12px;
    min-width: 44px;
    height: 44px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
    color: #F9F9F9;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.chat-send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(91, 169, 249, 0.3);
}

.chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.chat-send-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s;
}

.chat-send-btn:hover::before {
    left: 100%;
}

/* Chat Status */
.chat-status {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #B2AFC5;
}

.chat-status-text {
    opacity: 0.8;
}

/* Chat Actions - Legacy (hidden when dock is active) */
.chat-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px;
    transition: opacity 0.3s ease;
}

.chat-actions.dock-active {
    display: none;
}

.chat-actions .btn {
    flex: 1;
    max-width: 120px;
    font-size: 13px;
    padding: 8px 16px;
}

/* Chat Header with Title */
.chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #2C2738;
    background: linear-gradient(135deg, #1D1A2A 0%, #2C2738 100%);
}

.chat-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.chat-title {
    font-size: 16px;
    font-weight: 600;
    color: #FFFFFF;
    background: none;
    border: none;
    padding: 4px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-title:hover {
    background: rgba(255, 255, 255, 0.1);
}

.chat-title:focus {
    outline: none;
    background: rgba(91, 169, 249, 0.1);
    border: 1px solid #5BA9F9;
}

.chat-title-edit {
    background: rgba(91, 169, 249, 0.1);
    border: 1px solid #5BA9F9;
    color: #FFFFFF;
    resize: none;
}

.chat-title-edit::placeholder {
    color: #B2AFC5;
}

.chat-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10B981;
    display: inline-block;
    margin-left: 8px;
    animation: pulse 2s infinite;
}

.chat-status-indicator.inactive {
    background: #6B7280;
    animation: none;
}

/* Chat dock integration styles */
.chat-section.dock-mode {
    position: relative;
}

.chat-section.dock-mode .chat-container {
    height: 400px; /* Consistent height with dock mode */
    margin-bottom: 80px; /* Space for fixed dock */
}

/* Chat naming styles */
.chat-naming-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-rename-btn {
    opacity: 0;
    transition: opacity 0.2s ease;
    background: none;
    border: none;
    color: #B2AFC5;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
}

.chat-naming-container:hover .chat-rename-btn {
    opacity: 1;
}

.chat-rename-btn:hover {
    color: #5BA9F9;
    background: rgba(91, 169, 249, 0.1);
}

/* Pro feature indicators */
.chat-pro-feature {
    position: relative;
}

.chat-pro-feature::after {
    content: 'PRO';
    position: absolute;
    top: -4px;
    right: -4px;
    background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
    color: #000;
    font-size: 8px;
    font-weight: 700;
    padding: 2px 4px;
    border-radius: 4px;
    z-index: 1;
}

/* Empty Chat State */
.chat-messages.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #B2AFC5;
    font-style: italic;
    text-align: center;
}

.chat-messages.empty::before {
    content: '💬';
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Chat Loading State */
.chat-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #5BA9F9;
    font-size: 13px;
    padding: 8px 0;
}

.chat-loading-dots {
    display: flex;
    gap: 2px;
}

.chat-loading-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: loadingDots 1.4s infinite ease-in-out;
}

.chat-loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.chat-loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* History Tabs */
.history-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid #2C2738;
}

.history-tab {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: #B2AFC5;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    position: relative;
}

.history-tab:hover {
    color: #F9F9F9;
    background: rgba(91, 169, 249, 0.1);
}

.history-tab.active {
    color: #5BA9F9;
    border-bottom-color: #5BA9F9;
    background: rgba(91, 169, 249, 0.1);
}

.history-tab.active::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(135deg, #5BA9F9 0%, #3F2B96 100%);
}

/* History Content */
.history-content {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chat History Actions */
.chat-history-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    background: #1A1A2E;
    border-radius: 12px;
    border: 1px solid #2C2738;
}

.chat-history-actions .btn {
    flex: 1;
    max-width: 150px;
    font-size: 13px;
    padding: 8px 12px;
}

/* Chat History Items */
.chat-history-item {
    background: #1D1A2A;
    border: 1px solid #2C2738;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.chat-history-item:hover {
    border-color: #5BA9F9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(91, 169, 249, 0.15);
}

.chat-history-item.active {
    border-color: #5BA9F9;
    background: rgba(91, 169, 249, 0.05);
}

.chat-history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
}

.chat-history-title {
    font-weight: 600;
    color: #F9F9F9;
    font-size: 14px;
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
}

.chat-history-date {
    font-size: 12px;
    color: #B2AFC5;
    white-space: nowrap;
}

.chat-history-preview {
    color: #B2AFC5;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-history-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #B2AFC5;
}

.chat-history-stats {
    display: flex;
    gap: 16px;
}

.chat-history-stat {
    display: flex;
    align-items: center;
    gap: 4px;
}

.chat-history-actions-inline {
    display: flex;
    gap: 8px;
}

.chat-history-actions-inline .btn {
    padding: 4px 8px;
    font-size: 11px;
    min-width: auto;
}

/* Empty Chat History State */
.chat-history-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #B2AFC5;
}

.chat-history-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.chat-history-empty-title {
    font-size: 18px;
    font-weight: 600;
    color: #F9F9F9;
    margin-bottom: 8px;
}

.chat-history-empty-desc {
    font-size: 14px;
    margin-bottom: 24px;
    max-width: 300px;
    line-height: 1.5;
}

/* Mobile Responsive */
@media (max-width: 640px) {
    .chat-container {
        height: 350px;
    }
    
    .chat-messages {
        padding: 12px;
        gap: 12px;
    }
    
    .message {
        max-width: 90%;
    }
    
    .chat-input-area {
        padding: 12px;
    }
    
    .chat-input-container {
        gap: 8px;
    }
    
    .chat-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .chat-actions .btn {
        max-width: none;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .chat-container {
        height: 300px;
    }
    
    .chat-messages {
        padding: 8px;
        gap: 8px;
    }
    
    .message {
        max-width: 95%;
        padding: 10px 12px;
    }
    
    .message-content {
        font-size: 13px;
    }
    
    #chatInput {
        font-size: 13px;
        padding: 10px 12px;
    }
    
    .chat-send-btn {
        min-width: 40px;
        height: 40px;
        padding: 10px;
    }
}